/* Footer Styles */
.footer {
  background: var(--secondary-bg);
  padding: var(--space-24) 0 var(--space-8);
  position: relative;
  overflow: hidden;
  border-top: 1px solid var(--border-primary);
}

/* Background Elements */
.footer__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.bg-element {
  position: absolute;
  background: var(--accent-gradient);
  border-radius: 50%;
  opacity: 0.05;
}

.bg-element--1 {
  width: 300px;
  height: 300px;
  top: 10%;
  right: 5%;
}

.bg-element--2 {
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: 10%;
}

/* Footer Content */
.footer__content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: var(--space-12);
  margin-bottom: var(--space-16);
  position: relative;
  z-index: 2;
}

.footer__section {
  display: flex;
  flex-direction: column;
}

/* Company Section */
.footer__company {
  max-width: 400px;
}

.company__logo {
  display: flex;
  align-items: center;
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
}

.logo__text {
  color: var(--text-primary);
  margin-right: var(--space-1);
}

.logo__accent {
  color: var(--accent-primary);
  position: relative;
}

.logo__accent::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--accent-gradient);
  border-radius: var(--radius-full);
}

.company__description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.company__stats {
  display: flex;
  gap: var(--space-6);
  flex-wrap: wrap;
}

.stat {
  text-align: center;
  min-width: 80px;
}

.stat__number {
  display: block;
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: var(--space-1);
}

.stat__label {
  font-size: var(--text-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Section Titles */
.section__title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  position: relative;
}

.section__title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--accent-gradient);
}

/* Links */
.links__list {
  list-style: none;
}

.links__item {
  margin-bottom: var(--space-3);
}

.links__link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-fast);
  position: relative;
  padding-left: var(--space-4);
}

.links__link::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--accent-primary);
  opacity: 0;
  transition: var(--transition-fast);
}

.links__link:hover {
  color: var(--text-primary);
  padding-left: var(--space-5);
}

.links__link:hover::before {
  opacity: 1;
}

/* Contact Section */
.contact__info {
  margin-bottom: var(--space-8);
}

.contact__item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.contact__icon {
  font-size: var(--text-xl);
  margin-top: var(--space-1);
}

.contact__details {
  display: flex;
  flex-direction: column;
}

.contact__label {
  font-size: var(--text-sm);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--space-1);
}

.contact__value {
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Newsletter */
.newsletter {
  background: var(--glass-bg);
  padding: var(--space-6);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-primary);
}

.newsletter__title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.newsletter__description {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-4);
  line-height: 1.4;
}

.newsletter__form {
  display: flex;
  gap: var(--space-2);
}

.newsletter__input {
  flex: 1;
  padding: var(--space-3);
  background: var(--tertiary-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-size: var(--text-sm);
  transition: var(--transition-fast);
}

.newsletter__input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.newsletter__input::placeholder {
  color: var(--text-muted);
}

.newsletter__button {
  padding: var(--space-3) var(--space-4);
  background: var(--accent-gradient);
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  white-space: nowrap;
}

.newsletter__button:hover {
  background: var(--accent-gradient-hover);
  transform: translateY(-1px);
}

/* Footer Bottom */
.footer__bottom {
  border-top: 1px solid var(--border-primary);
  padding-top: var(--space-8);
  position: relative;
  z-index: 2;
}

.footer__bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-6);
}

/* Social Links */
.footer__social {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.social__link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--glass-bg);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition-normal);
  font-size: var(--text-sm);
}

.social__link:hover {
  background: var(--accent-primary);
  color: var(--primary-bg);
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.social__icon {
  font-size: var(--text-base);
}

.social__name {
  font-weight: 500;
}

/* Copyright */
.footer__copyright {
  color: var(--text-muted);
  font-size: var(--text-sm);
  text-align: center;
}

.footer__link {
  color: var(--accent-primary);
  text-decoration: none;
  margin: 0 var(--space-2);
  transition: var(--transition-fast);
}

.footer__link:hover {
  color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer__content {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
  }
  
  .footer__company {
    grid-column: 1 / -1;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: var(--space-16) 0 var(--space-6);
  }
  
  .footer__content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
    margin-bottom: var(--space-12);
  }
  
  .company__stats {
    justify-content: center;
  }
  
  .newsletter__form {
    flex-direction: column;
  }
  
  .footer__bottom-content {
    flex-direction: column;
    text-align: center;
  }
  
  .footer__social {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer__social {
    flex-direction: column;
    align-items: center;
  }
  
  .social__link {
    width: 200px;
    justify-content: center;
  }
  
  .company__stats {
   
    align-items: center;
    gap: var(--space-4);
  }
}
