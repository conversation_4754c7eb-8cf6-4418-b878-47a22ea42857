{"name": "realstate-backend", "version": "1.0.0", "description": "Backend API for Real Estate Website with Admin Panel", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["real-estate", "mern", "api", "admin-panel"], "author": "Fabaf Projects", "license": "ISC", "type": "commonjs", "dependencies": {"@reduxjs/toolkit": "^2.8.2", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "morgan": "^1.10.1", "multer": "^2.0.2", "react-redux": "^9.2.0", "react-toastify": "^11.0.5"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10"}}